<template>
  <div class="qualitative-audit">
    <el-form
      ref="auditForm"
      :inline="false"
      class="demo-form-inline"
      :model="auditForm"
      label-width="130px"
      :rules="auditFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障所属专业:" prop="professionalType">
              {{ auditForm.professionalTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障等级:" prop="faultLevel">
              {{ auditForm.faultLevelName }}
            </el-form-item>
          </el-col>
          <el-col :span="8"></el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime">
              {{ auditForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime">
              {{ auditForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复时间:" prop="busRecoverTime">
              {{ auditForm.busRecoverTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:">
              {{ second2Time(auditForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:">
              {{ second2Time(auditForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:">
              {{ second2Time(auditForm.processDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复历时:">
              {{ second2Time(auditForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="是否超时:" prop="isOverTime">
              <span v-if="auditForm.isOverTime == 0">否</span>
              <span v-else-if="auditForm.isOverTime == 1">是</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept">
              {{ auditForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person">
              {{ auditForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人电话:" prop="personPhone">
              {{ auditForm.personPhone }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <span v-if="auditForm.isEffectBusiness == 0">否</span>
              <span v-else-if="auditForm.isEffectBusiness == 1">是</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否铁塔原因:" prop="isTowerFault">
              <span v-if="auditForm.isTowerFault == 0">否</span>
              <span v-else-if="auditForm.isTowerFault == 1">是</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否基站退服:" prop="isSiteOffline">
              <span v-if="auditForm.isSiteOffline == 0">否</span>
              <span v-else-if="auditForm.isSiteOffline == 1">是</span>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="退服原因:" prop="siteOfflineReason">
              {{ auditForm.siteOfflineReasonName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理方式:" prop="faultHandleType">
              {{ auditForm.faultHandleTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="同行处理人:" prop="withinPerson">
              {{ auditForm.withinPerson }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否需要定性审核:"
              prop="needCheck"
              style="white-space: nowrap"
            >
              <span v-if="auditForm.needCheck == 0">否</span>
              <span v-else-if="auditForm.needCheck == 1">是</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 3px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="auditForm.faultCate"
                :dictId="70005"
                style="width: 100%"
                placeholder="请选择内容"
                @change="changeFaultCate"
              />
            </el-form-item>
          </el-col>
          <!-- 故障厂家字段 - 仅在故障分类为"基站设备"或"传输系统"时显示 -->
          <el-col :span="8" v-if="showVendorField">
            <el-form-item
              label="故障厂家:"
              prop="faultVendor"
            >
              <el-select
                v-model="auditForm.faultVendor"
                placeholder="请选择故障厂家"
                style="width: 100%"
              >
                <el-option
                  v-for="item in vendorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              prop="faultReason"
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
            >
              <dict-select
                :value.sync="auditForm.faultReason"
                :dictId="dictTypeCode"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <!-- 设备类型字段 -->
          <el-col :span="8">
            <el-form-item
              label="设备类型:"
              prop="deviceType"
              :rules="{
                required: isUnicomToUnicom,
                message: '请选择设备类型',
              }"
            >
              <el-select
                v-model="auditForm.deviceType"
                placeholder="请选择设备类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in deviceTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 设备名称字段 -->
          <el-col :span="8">
            <el-form-item
              label="设备名称:"
              prop="deviceName"
              :rules="{
                required: isUnicomToUnicom,
                message: '请输入设备名称',
              }"
            >
              <el-input
                v-model="auditForm.deviceName"
                placeholder="请输入设备名称"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="附件:" prop="attachmentName">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileArr"
                :key="index"
                @close="closeAndDeleteFile(item)"
                @click="downloadAppendixFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="closeFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="faultReasonDesc"
              :rules="{
                required: true,
                message: '请填写故障原因描述',
                trigger: 'blur',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="auditForm.faultReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="auditForm.faultComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 3px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item label="审核结果" prop="auditResult">
              <el-radio-group v-model="auditForm.auditResult">
                <el-radio label="Y">同意</el-radio>
                <el-radio label="N">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="定性意见" prop="auditContent">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请填写定性意见"
                v-model="auditForm.auditContent"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input></el-form-item
          ></el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="包机人评定"
        :body-style="{ padding: '20px' }"
        style="margin-top: 3px"
        class="cus-card"
        v-if="auditForm.auditResult == 'Y'"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="修障质量" prop="repairQuality">
              <el-rate
                v-model="auditForm.repairQuality"
                :texts="descriText"
              ></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="反馈质量" prop="fdbkQuality">
              <el-rate
                v-model="auditForm.fdbkQuality"
                :texts="descriText"
              ></el-rate> </el-form-item
          ></el-col>
          <el-col :span="8">
            <el-form-item label="满意度" prop="satisfaction">
              <el-rate
                v-model="auditForm.satisfaction"
                :texts="descriText"
              ></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="评价意见" prop="evaluationOpinion">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请填写评价意见"
                v-model="auditForm.evaluationOpinion"
                style="width: 100%"
                show-word-limit
                maxlength="1000"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import {
  apiQualitativeReview,
  getCurrentTime,
  apiDownloadAppendixFile,
  apiDeleteFdFile,
  apiqueryFeedback,
} from "../api/CommonApi";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import FileUpload from "../../../workOrder/components/FileUpload.vue";
import { mixin } from "../../../../../../mixins";

export default {
  name: "Audit",
  props: {
    common: Object,
    actionName: String,
    woId: String,
  },
  components: { DictSelect, FileUpload },
  mixins: [mixin],
  data() {
    return {
      auditForm: {
        auditContent: "",
        repairQuality: 5,
        fdbkQuality: 5,
        satisfaction: 5,
        evaluationOpinion: "",
        // 新增字段
        faultVendor: null, // 故障厂家
        deviceType: null, // 设备类型
        deviceName: null, // 设备名称
      },
      auditFullScreenLoading: false,
      descriText: ["很不满意", "不满意", "一般", "很满意", "非常满意"],
      // 测试用：派单方式
      testDispatchMode: 'unicom_to_unicom', // unicom_to_unicom, unicom_to_telecom, telecom_to_unicom
      // 故障厂家选项
      baseStationVendorOptions: [
        { label: '华为', value: 'huawei' },
        { label: '中兴', value: 'zte' },
        { label: '爱立信', value: 'ericsson' },
        { label: '诺基亚', value: 'nokia' },
        { label: '中信科', value: 'cict' },
        { label: '其他', value: 'other' }
      ],
      transmissionVendorOptions: [
        { label: '华为', value: 'huawei' },
        { label: '中兴', value: 'zte' },
        { label: '烽火', value: 'fiberhome' },
        { label: '上海贝尔', value: 'alcatel' },
        { label: '其它', value: 'other' }
      ],
      // 设备类型选项
      deviceTypeOptions: [
        { label: 'BSC', value: 'BSC' },
        { label: 'BTS', value: 'BTS' },
        { label: 'RNC', value: 'RNC' },
        { label: 'NodeB', value: 'NodeB' },
        { label: 'eNodeB', value: 'eNodeB' },
        { label: '小区(2/3/4G)', value: 'cell_234g' }
      ],
      auditFormRule: {
        auditResult: [{ required: true, message: "请选择审核结果" }],

        repairQuality: [{ required: true, message: "请打分" }],
        fdbkQuality: [{ required: true, message: "请打分" }],
        satisfaction: [{ required: true, message: "请打分" }],
        evaluationOpinion: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "auditForm",
            messsage: "已超过填写字数上限",
          },
        ],
        auditContent: [
          {
            validator: this.checkLength,
            max: 255,
            form: "auditForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      dictTypeCode: null,
      fddxFileArr: [],
      importForm: {
        relatedFilesFileList: [],
      },
      relatedFilesDialogVisible: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    // 是否为联通派联通模式
    isUnicomToUnicom() {
      return this.testDispatchMode === 'unicom_to_unicom';
    },
    // 是否显示故障厂家字段
    showVendorField() {
      // 仅在故障分类为"基站设备"或"传输系统"时显示
      const faultCateValue = this.auditForm.faultCate;
      return faultCateValue === '基站设备' || faultCateValue === '传输系统' || faultCateValue === '1';
    },
    // 根据故障分类获取对应的厂家选项
    vendorOptions() {
      const faultCateValue = this.auditForm.faultCate;
      if (faultCateValue === '基站设备' || faultCateValue === '1') {
        return this.baseStationVendorOptions;
      } else if (faultCateValue === '传输系统') {
        return this.transmissionVendorOptions;
      }
      return [];
    },
  },
  mounted() {
    this.getFeedbackData();
  },
  watch: {
    "auditForm.auditResult": {
      handler: function (val) {
        if (val == "Y") {
          this.auditForm.auditContent = "同意";
        } else {
          this.auditForm.auditContent = "";
        }
      },
    },
  },
  methods: {
    getFeedbackData() {
      let param = {
        woId: this.woId,
        isShowWu: "0",
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let feedBackData = res?.data?.rows ?? [];
            if (feedBackData.length > 0) {
              this.auditForm = { ...this.auditForm, ...feedBackData[0] };
              this.$set(this.auditForm, "auditResult", "");
              this.$set(this.auditForm, "auditContent", "");
              this.$set(this.auditForm, "repairQuality", 5);
              this.$set(this.auditForm, "fdbkQuality", 5);
              this.$set(this.auditForm, "satisfaction", 5);

              this.changeFaultCate("init");

              if (feedBackData[0].appendix) {
                this.fddxFileArr = JSON.parse(feedBackData[0].appendix);
              }
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    handleAuditSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          let self = this;
          self.auditFullScreenLoading = true;
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          let paramter = {
            woId: self.common.woId,
            workItemId: self.common.workItemId,
            processInstId: self.common.processInstId,
            auditUser: self.userInfo.userName,
            auditTime: getCurrentTime(Date.now()),
          };
          formData.append("sheetInfo", JSON.stringify(paramter));
          if (self.auditForm.auditResult == "N") {
            delete self.auditForm.repairQuality;
            delete self.auditForm.fdbkQuality;
            delete self.auditForm.satisfaction;
            delete self.auditForm.evaluationOpinion;
          }
          self.$set(self.auditForm, "actionName", self.actionName);
          formData.append("faultInfo", JSON.stringify(self.auditForm));
          let evaluateInfo = {
            woId: self.common.woId,
            evaluateId: self.auditForm.evaluateId,
            auditContent: self.auditForm.auditContent,
            auditResult: self.auditForm.auditResult,
            fdbkQuality: self.auditForm.fdbkQuality,
            repairQuality: self.auditForm.repairQuality,
            satisfaction: self.auditForm.satisfaction,
            evaluationOpinion: self.auditForm.evaluationOpinion,
          };
          formData.append("evaluateInfo", JSON.stringify(evaluateInfo));
          apiQualitativeReview(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("定性审核提交成功");
                this.$emit("qualitativeReviewSubmit", res.data);
              } else {
                this.$message.error(res.msg);
              }
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
            })
            .finally(() => {
              this.auditFullScreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      // this.auditForm = {
      //   ...this.$options.data,
      // };
      this.auditForm.auditResult = "";
      this.auditForm.auditContent = "";
      this.auditForm.evaluationOpinion = "";
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    changeFaultCate(type) {
      if (type != "init") {
        this.auditForm.faultReason = "";
      }
      if (this.auditForm.faultCate == "1") {
        this.dictTypeCode = "70006";
      } else if (this.auditForm.faultCate == "2") {
        this.dictTypeCode = "70007";
      } else if (this.auditForm.faultCate == "3") {
        this.dictTypeCode = "70008";
      } else if (this.auditForm.faultCate == "4") {
        this.dictTypeCode = "70009";
      } else if (this.auditForm.faultCate == "5") {
        this.dictTypeCode = "70010";
      } else if (this.auditForm.faultCate == "6") {
        this.dictTypeCode = "70011";
      } else if (this.auditForm.faultCate == "7") {
        this.dictTypeCode = "70012";
      } else if (this.auditForm.faultCate == "8") {
        this.dictTypeCode = "70013";
      } else if (this.auditForm.faultCate == "9") {
        this.dictTypeCode = "70014";
      }
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.auditForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fddxFileArr.indexOf(tag), 1);
            this.auditForm.appendix = JSON.stringify(this.fddxFileArr);
            this.$message.success("附件删除成功");
          } else {
            this.$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (
        this.importForm.relatedFilesFileList.length == 0 &&
        this.fddxFileArr.length == 0
      ) {
        this.auditForm.attachmentName = null;
      }
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.auditForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    onReset() {
      this.auditForm.auditResult = "";
      this.auditForm.auditContent = "";
      this.auditForm.evaluationOpinion = "";
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative-audit {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }

  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
.custom-theme-default .el-rate {
  padding-top: 5px !important;
}
</style>
